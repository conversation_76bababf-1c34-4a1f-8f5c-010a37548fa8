import type { Testimonial } from "@shared/types";

export const testimonials: Testimonial[] = [
  {
    id: 1,
    text: "The atmosphere is so cozy and inviting, and their house blend coffee is absolutely perfect. This has become my go-to spot for both work meetings and catching up with friends.",
    name: "<PERSON>",
    title: "Local Resident",
    rating: 5
  },
  {
    id: 2,
    text: "I've tried many cafés in the area, but Aroma stands out for their attention to detail. Their avocado toast is incredibly fresh, and the latte art always brings a smile to my face.",
    name: "<PERSON>",
    title: "Food Blogger",
    rating: 5
  },
  {
    id: 3,
    text: "As someone who works remotely, I appreciate the reliable WiFi and peaceful environment. The staff remembers my usual order and always makes me feel welcome. It's like my second home!",
    name: "<PERSON>",
    title: "Freelance Designer",
    rating: 4.5
  }
];
